import { useCashRegisterStore } from '@/stores/cash-register';
import cashApi, { setDeviceId } from '@/util/cashAxios';

export const htmlLog = (...msg: unknown[]) => {
  const log = document.createElement('div');
  log.innerText = msg.join(', ');
  document.getElementById('log')?.appendChild(log);
};

export enum AndroidActions {
  INIT = 'deviceSettings',
}

export const requestAndroid = (data: Record<string, string>) => {
  window.JSBridge?.sendRequest(JSON.stringify(data));
};

export const handleAndroidEvent = async(e: Event) => {
  const cashRegisterStore = useCashRegisterStore();
  const { action, ...rest } = (e as CustomEvent).detail;

  if (action === AndroidActions.INIT) {
    const { actualDisplay, displays, uuid } = rest;
    setDeviceId(uuid);
    const { data: { data }} = await cashApi.post('/api/cash-register/init', {
      frontends: displays.map(({ id, resolution }) => ({ display: id.toString(), resolution })),
    });
    cashRegisterStore.init(actualDisplay, data);
  }
};
