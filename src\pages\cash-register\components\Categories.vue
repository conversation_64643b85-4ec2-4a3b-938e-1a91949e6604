<script setup lang="ts">
import { IconChevronLeft, IconSearch } from '@tabler/icons-vue';
import { computed, onMounted, reactive, ref } from 'vue';
import CategoriesAddProductModal from '@/pages/cash-register/dialogs/components/CategoriesAddProductModal.vue';
import { contrastTextColor } from '@/util/cash-register-utils';
import cashApi from '@/util/cashAxios';
import { getFromMultiLangObject } from '@/util/multilang';
import type { Product, Tree } from '@/util/types/api-responses';

const ROOT_KEY = 'root';

const modalData = ref<Product>();
const visibleAddModal = ref(false);
const rootCategories = ref<Tree>([]);
const path = ref<Tree>([]);
const search = ref('');
const searchedProducts = ref<Product[]>([]);
const productsCache = reactive<Record<string, Product[]>>({});

const selectedCategory = computed(() => path.value[path.value.length - 1] ?? null);

const currentList = computed(() =>
  path.value.length
    ? path.value[path.value.length - 1].children
    : rootCategories.value,
) as unknown as Tree;

const currentProducts = computed(() => {
  if (search.value.length > 2) {
    return searchedProducts.value;
  }

  return productsCache[selectedCategory.value?.id ?? ROOT_KEY] || [];
});

const getProducts = async(catId?: string) => {
  const key = catId ?? ROOT_KEY;
  if (!productsCache[key]) {
    const params = catId
      ? { 'filter[category_id]': catId }
      : {};
    const { data: { data }} = await cashApi.get('api/cash-register/products', { params });
    productsCache[key] = data;
  }
};

const selectCategory = async(cat: Tree[number]) => {
  search.value = '';
  path.value.push(cat);
  await getProducts(cat.id);
};

const searchProducts = async(e: any) => {
  const { value } = e.target;
  if (value.length < 3) {
    return;
  }

  if (!value) {
    searchedProducts.value = [];
  }

  const { data: { data }} = await cashApi.get('api/cash-register/products', {
    params: {
      search: value,
    },
  });

  searchedProducts.value = data;
};

const openAddModal = (product: Product) => {
  visibleAddModal.value = true;
  modalData.value = product;
};

onMounted(async() => {
  const { data: { data }} = await cashApi.get('api/cash-register/products/categories/tree');
  rootCategories.value = data;
  await getProducts();
});
</script>

<template>
  <div class=" p-4 overflow-y-scroll bg-gray-100">
    <header class="fig mb-4">
      <div class="flex items-center gap-3">
        <button
          v-if="path.length"
          class="flex items-center bg-white text-lg font-medium text-nowrap border pr-4 pl-2 py-2 rounded-lg"
          @click="path.pop()"
        >
          <IconChevronLeft />
          <span>{{ path.length > 1 ? getFromMultiLangObject(path.at(-2)?.label) : 'All Categories' }}</span>
        </button>
        <h3 class="font-bold text-3xl text-nowrap">{{ path.length ? getFromMultiLangObject(path.at(-1)?.label) : $t('cash-register.all-categories') }}</h3>
      </div>

      <div class="rounded-lg border border-blacks bg-white w-2/3 ml-auto text-lg pl-2.5 py-2 fig">
        <IconSearch size="24" class="text-black/50" />
        <input v-model="search" type="text" class="focus:outline-none w-full" :placeholder="$t('misc.search')" @input="searchProducts">
      </div>
    </header>

    <section class="fig flex-wrap gap-4">
      <button
        v-for="cat in currentList"
        :key="cat.id"
        :style="{ background: cat.color, color: contrastTextColor(cat.color!) }"
        class="h-24 w-32 rounded-md p-2 grid place-content-center text-xl border"
        @click="selectCategory(cat)"
      >
        {{ getFromMultiLangObject(cat.label) }}
      </button>
    </section>

    <hr v-if="currentList?.length" class="my-3">

    <section class="flex items-start flex-wrap gap-2">
      <div v-for="prod in currentProducts" :key="prod.id" class="size-[200px] font-medium text-xl" @click="openAddModal(prod)">
        <div :style="{ background: `url(${prod.cover_image_thumb})` }" :class="['h-full !bg-cover flex flex-col justify-between rounded-md border overflow-hidden']">
          <div class="flex items-center justify-between m-1">
            <div class="bg-emerald-500 px-1 flex items-center gap-1 rounded text-white">
              <span class="text-sm leading-5">PLU:</span>
              <span>{{ prod.plu }}</span>
            </div>
            <div class="bg-white leading-5 px-1 rounded">{{ prod.price != null ? prod.price + '€' : '' }}</div>
          </div>
          <div class="bg-black/60 p-1 text-white">{{ getFromMultiLangObject(prod.name) }}</div>
        </div>
      </div>
    </section>

    <CategoriesAddProductModal v-if="visibleAddModal" :product="modalData!" @close="visibleAddModal = false" />
  </div>
</template>
