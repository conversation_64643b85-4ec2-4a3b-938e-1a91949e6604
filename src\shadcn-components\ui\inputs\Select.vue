<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import adminApi from '@/util/adminAxios';

const { locale } = useI18n();

const props = defineProps<{
  defaultValue?: any
  modelValue?: any
  disabled?: boolean
  errors?: string[]
  options?: any
  itemTitle?: string
  itemValue?: string
  multiple?: boolean
  clearable?: boolean
  allowCreate?: boolean
  multilang?: boolean
  collapseTags?: boolean
  placeholder?: string
  filterable?: boolean
  autocomplete?: boolean
  endpoint?: string
}>();

const emits = defineEmits<{(e: 'update:modelValue', payload: string | number): void;
  (e: 'update:search', payload: string): void;
  (e: 'focus'): void;
  (e: 'created', item: any): any;
}>();

const modelValue = useVModel(props, 'modelValue', emits, {
  passive: true,
  defaultValue: props.defaultValue,
});

watch(() => props.options, (newOptions) => {
  opts.value = newOptions;
});

const opts = ref<any[]>(props.options);
const loading = ref(false);

const remoteMethod = async(query: string) => {
  let queryParams = {};
  if (query && query.length > 3) {
    queryParams = { search: query };
  }
  loading.value = true;
  try {
    const { data: rcps } = await adminApi.get(props.endpoint!, { params: queryParams });
    opts.value = rcps.data;
  } catch (error) {
    console.error('Error loaded data:', error);
    opts.value = [];
  }
  loading.value = false;
};

const checkIfCreatedNew = (item: any) => {
  if (props.allowCreate && opts.value.find(opt => opt.id !== item)) {
    emits('created', item);
  }
};
</script>

<template>
  <div class="w-full relative">
    <el-select
      v-model="modelValue"
      :filterable="autocomplete || filterable"
      :remote="autocomplete"
      reserve-keyword
      :disabled
      :clearable
      :allow-create
      value-key="id"
      :collapse-tags
      collapse-tags-tooltip
      :max-collapse-tags="3"
      :multiple
      :placeholder="placeholder ?? ''"
      :remote-method="autocomplete ? remoteMethod : undefined"
      :loading="loading"
      :class="[errors?.length && 'select-error', 'shadow rounded-md']"
      @change="checkIfCreatedNew"
    >
      <el-option
        v-for="item in opts"
        :key="itemValue ? item[itemValue] : item"
        :label="itemTitle ? multilang ? item[itemTitle][locale] : item[itemTitle] : item"
        :value="itemValue ? item[itemValue] : item"
      />
    </el-select>

    <div class="absolute top-10">
      <div v-for="error in errors" :key="error" class="text-rose-500 text-xs mt-0.5 ml-1.5">{{ error }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-select__wrapper) {
  min-height: 40px !important;
  padding: 8px 14px !important;
  border-radius: 0.375rem !important;
  border: 1px solid hsl(var(--input)) !important;
  box-shadow: none !important;
  transition: none !important;
  &:hover {
    box-shadow: none !important;
  }
  &:focus-within {
    outline: 2px solid #d2e2e7 !important;
    outline-offset: -3px;
    border: 1px solid transparent !important;
  }
}

:deep(.select-error .el-select__wrapper) {
  border: 1px solid theme('colors.rose.500') !important;
  background-color: theme('colors.rose.50/50') !important;
  &:focus-within {
    border: 2px solid theme('colors.rose.500') !important;
    outline: none !important;
  }
}
</style>
