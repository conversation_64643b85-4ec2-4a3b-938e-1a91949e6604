<script setup lang="ts">
import { IconDots } from '@tabler/icons-vue';
import { ref } from 'vue';
import { htmlLog } from '@/pages/cash-register/androidRequestHandler';
import ListEditProductModal from '@/pages/cash-register/dialogs/components/ListEditProductModal.vue';
import { useCashRegisterStore } from '@/stores/cash-register';
import cashApi from '@/util/cashAxios';
import { getFromMultiLangObject } from '@/util/multilang';
import type { CRCartItem } from '@/util/types/api-responses';

const cashRegisterStore = useCashRegisterStore();

const editModalVisible = ref(false);
const editProductData = ref<CRCartItem>();

const showEditModal = (product: CRCartItem) => {
  editProductData.value = product;
  editModalVisible.value = true;
};

const pay = async() => {
  try {
    cashApi.get(`/api/cash-register/orders`);

    const { data: { data }} = await cashApi.get(`/api/cash-register/orders/${cashRegisterStore.currentCartID}`);
    await cashApi.patch(`/api/cash-register/orders/${cashRegisterStore.currentCartID}`, {
      ...data,
      status: 'PAID',
    });
    cashRegisterStore.cart = [];
    cashApi.get(`/api/cash-register/orders`);
  } catch (error) {
    htmlLog(error);
  }
};
</script>

<template>
  <div class="flex flex-col border-r border-gray-300">
    <div class="flex-1 p-3 gap-2 overflow-y-scroll">
      <div v-if="cashRegisterStore.cart.length" class="grid gap-2.5">
        <div v-for="item in cashRegisterStore.cart" :key="item.id" class="flex sh-[84px] bg-white border border-gray-300 rounded-lg ">
          <div class="p-2.5 font-medium">
            <div class="text-xl line-clamp-2">{{ getFromMultiLangObject(item.name) }}</div>
            <div class="text-lg text-black/50">{{ item.quantity }}x &bullet; {{ item.price }}€</div>
          </div>

          <div class="ml-auto">
            <div class="h-1/2 !w-12 ml-auto flex items-center text-green-500" @click="showEditModal(item)">
              <IconDots size="30" />
            </div>
            <div class="h-1/2 font-bold text-2xl pr-3">{{ (item.quantity * item.price).toFixed(2) }}€</div>
          </div>
        </div>
      </div>
      <div v-else class="grid place-content-center h-full text-lg text-gray-400 italic">
        Zoznam poloziek je prazdny
      </div>
    </div>

    <hr class="bg-green-600">
    <div class="font-bold flex items-center justify-between px-3 mt-3">
      <div class="text-2xl">Celkom:</div>
      <div class="text-3xl">{{ cashRegisterStore.cart.reduce((a, b) => a + (b.price * b.quantity), 0).toFixed(2) }}€</div>
    </div>

    <div class="fig mt-3 pt-0 p-3">
      <button class="bg-gray-600 text-white w-1/3 p-5 rounded-lg text-2xl font-medium">Zlava</button>
      <button class="bg-green-600 text-white w-2/3 p-5 rounded-lg text-2xl font-medium" @click="pay">Zaplatit</button>
    </div>

    <ListEditProductModal v-if="editModalVisible" :product="editProductData!" @close="editModalVisible = false" />
  </div>
</template>
