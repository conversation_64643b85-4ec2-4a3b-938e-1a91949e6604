import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { configureEcho } from '@laravel/echo-vue';
import { createHead } from '@unhead/vue';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import { createApp, defineAsyncComponent } from 'vue';
import Toast from 'vue-toastification';
import { loadLayoutMiddleware } from '@/router/middleware/layout-middleware';
import icons from '@/setup/import/icons';
import { TokenService, useAuthStore } from '@/stores/auth-store';
import { TokenService as CashTokenService } from '@/stores/cash-auth-store';
import type { PluginOptions } from 'vue-toastification';
import 'dayjs/locale/zh-cn';
import '@/assets/fonts/fonts.css';
import 'gridstack/dist/gridstack.min.css';

window.callAndroid = json => window.JSBridge.sendRequest(json);
window.fetchAndroidData = () => {
  return new Promise(resolve => {
    window.receiveResponse = (jsonString: string) => {
      try {
        resolve(JSON.parse(jsonString));
      } catch {
        resolve(jsonString);
      }
    };
  });
};

icons.addIconsToLibrary();

const options: PluginOptions = {
  transition: 'Vue-Toastification__custom',
  maxToasts: 7,
  newestOnTop: true,
};

const head = createHead();

configureEcho({
  broadcaster: 'reverb',
  key: import.meta.env.VITE_REVERB_APP_KEY,
  wsHost: import.meta.env.VITE_REVERB_HOST ?? '127.0.0.1',
  wsPort: import.meta.env.VITE_REVERB_PORT,
  wssPort: import.meta.env.VITE_REVERB_PORT,
  forceTLS: (import.meta.env.VITE_REVERB_SCHEME ?? 'https') === 'https',
  enabledTransports: ['ws', 'wss'],
  disableStats: true,
  debug: false,
  authEndpoint: import.meta.env.VITE_APP_API_URL + '/broadcasting/auth',
  bearerToken: CashTokenService.getAccessToken(),
});

const createAndMountApp = async() => {
  import('@mdi/font/css/materialdesignicons.css');
  import('@/assets/css/vue-toastification/_toastification.scss');
  await import('@/App.vue');
  const App = defineAsyncComponent(() => import('@/App.vue'));

  const piniaStore = await import('@/pinia');
  const app = createApp(App).use(piniaStore.default);
  if (TokenService.getAccessToken()) {
    await useAuthStore()
      .fetchUser();
  }
  const router = await import('./router');
  router.default.beforeEach(async(to, from, next) => {
    await loadLayoutMiddleware(to, from, next, app);
    next();
  });
  const i18n = await import('@/i18n');

  app.directive('urlify', {
    mounted(el) {
      const urlPattern = /(https?:\/\/[^\s]+)/g;
      el.innerHTML = el.innerHTML.replace(urlPattern, '<a href="$&" target="_blank" class="text-blue-500 underline">$&</a>');
    },
  });

  app.use(router.default)
    .component('font-awesome-icon', FontAwesomeIcon)
    .use(i18n.default)
    .use(Toast, options)
    .use(ElementPlus)
    .provide('i18n_instance', i18n.default)
    .use(head);

  app.mount('#app');
  import('@/assets/styles.scss');

  return app;
};

export default createAndMountApp()
  .catch(() => console.error('Failed to init vue app, check backend server.'));
