<script setup lang="ts">
import { IconLockFilled, IconCircle, IconCircleFilled } from '@tabler/icons-vue';
import { nextTick, ref, watch } from 'vue';
import Keypad from '@/pages/cash-register/dialogs/components/Keypad.vue';
import { Dialog, DialogContent } from '@/shadcn-components/ui/dialog';
import { useCashAuthStore } from '@/stores/cash-auth-store';
import cashApi from '@/util/cashAxios';

const cashAuthStore = useCashAuthStore();

const isOpened = defineModel<boolean>();
const emits = defineEmits(['onSuccess']);

const eidInput = ref();
const eid = ref<string[]>([]);
const pin = ref<(string | null)[]>([null, null, null, null]);
const eidChecked = ref(false);
const pinDotsRef = ref();
const pinIndex = ref(0);

const addDigitPIN = async(digit: string) => {
  if (pinIndex.value > 3) {
    return;
  }

  pin.value[pinIndex.value++] = digit;

  if (pinIndex.value === 4) {
    try {
      await cashAuthStore.login(eid.value.join(''), pin.value.join(''));

      isOpened.value = false;
      emits('onSuccess');
      resetForm();
    } catch {
      shakeItOff();
      pinIndex.value = 0;
      pin.value = [null, null, null, null];
    }
  }
};

const deleteDigitPIN = () => {
  if (pinIndex.value === 0) {
    return;
  }

  pin.value[--pinIndex.value] = null;
};

const addDigitEID = (n: string) => {
  eid.value.push(n);
  scrollToEnd();
};

const deleteDigitEID = () => {
  eid.value.pop();
  scrollToEnd();
};

const checkEID = async() => {
  try {
    await cashApi.post('/api/cash-register/auth/check', { eid: eid.value.join('') });
    eidChecked.value = true;
  } catch {
    alert('bad eid');
  }
};

const preventClose = (e: any) => {
  e.preventDefault();
  e.stopPropagation();
};

const shakeItOff = () => {
  if (pinDotsRef.value) {
    pinDotsRef.value.classList.remove('animate-shake');
    void pinDotsRef.value.offsetWidth;
    pinDotsRef.value.classList.add('animate-shake');
  }
};

const scrollToEnd = () => {
  nextTick(() => {
    const el = eidInput.value;
    if (!el) {
      return;
    }
    el.scrollLeft = el.scrollWidth;
  });
};

const resetForm = (hardReset?: boolean) => {
  if (hardReset) {
    eid.value = [];
    eidChecked.value = false;
  } else {
    eid.value = localStorage.getItem('last-eid')?.split('') ?? [];
    eidChecked.value = Boolean(eid.value);
  }
  pin.value = [null, null, null, null];
  pinIndex.value = 0;
};

watch(() => isOpened, () => {
  eid.value = localStorage.getItem('last-eid')?.split('') ?? [];
  eidChecked.value = Boolean(eid.value.length);
});
</script>

<template>
  <Dialog v-model:open="isOpened">
    <DialogContent no-close :close-on-outside-click="false" class="max-w-[480px]" @interact-outside="preventClose">
      <div class="mx-auto mt-2 flex flex-col items-center text-lg gap-2">
        <IconLockFilled size="40" />
        <div v-if="eidChecked" class="flex items-center gap-1">
          <div class="text-black/70">{{ $t('cash-register.cashier') }}:</div>
          <div class="font-medium">{{ cashAuthStore.user?.name }}</div>
        </div>
      </div>

      <div v-if="eidChecked">
        <div ref="pinDotsRef" class="grid grid-cols-4 text-black mb-8">
          <div v-for="(digit, i) in pin" :key="i" class="w-full">
            <IconCircleFilled v-if="digit !== null" class="mx-auto text-black" />
            <IconCircle v-else class="mx-auto text-black/50" />
          </div>
        </div>

        <Keypad @add-digit="addDigitPIN" @delete-digit="deleteDigitPIN" />
        <button class="w-full bg-black active:bg-black/80 rounded-md h-[50px] text-white font-medium mt-8" @click="resetForm(true)">{{ $t('cash-register.change-the-cashier') }}</button>
      </div>

      <div v-else class="w-full space-y-8">
        <div>
          <label for="eid">EID:</label>
          <input ref="eidInput" :value="eid.join('')" disabled class="w-full bg-white font-medium text-3xl tracking-widest border-b-2 border-black">
        </div>

        <Keypad @add-digit="addDigitEID" @delete-digit="deleteDigitEID" />
        <button class="w-full bg-black active:bg-black/80 rounded-md h-[50px] text-white font-medium" @click="checkEID">{{ $t('login.signIn') }}</button>
      </div>
    </DialogContent>
  </Dialog>
</template>
