<script setup lang="ts">
import { Check, Edit, Trash2, X } from 'lucide-vue-next';
import { onMounted, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import PageLoader from '@/components/global/PageLoader.vue';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/shadcn-components/ui/alert-dialog';
import { Button } from '@/shadcn-components/ui/button';
import Input from '@/shadcn-components/ui/inputs/Input.vue';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import adminApi from '@/util/adminAxios';
import { cloneObject } from '@/util/objects';
import { deployToast, ToastType } from '@/util/toast';
import type { Tax } from '@/util/types/api-responses';

const { t } = useI18n();

const taxes = ref<Tax[]>([]);
const formerTaxes = ref<Tax[]>([]);
const loading = ref(false);

const creatingNew = ref(false);
const editingId = ref();

const newItem = reactive<Partial<Pick<Tax, 'name' | 'rate'>>>({
  name: '',
  rate: undefined,
});

const getTaxes = async() => {
  const to = setTimeout(() => {
    loading.value = true;
  }, 220);
  const { data } = await adminApi.get('/api/admin/vat-rates');
  clearTimeout(to);
  loading.value = false;
  return data.data;
};

const createNewRole = async() => {
  try {
    await adminApi.post('/api/admin/vat-rates', newItem);
    taxes.value = await getTaxes();
    formerTaxes.value = cloneObject(taxes.value);
    resetNewItem();
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
};

const resetNewItem = () => {
  newItem.name = '';
  newItem.rate = undefined;
  creatingNew.value = false;
};

const saveEdits = async(tax: Tax) => {
  try {
    await adminApi.put(`/api/admin/vat-rates/${tax.id}`, tax);
    editingId.value = undefined;
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
};

const removeTax = async(id: string) => {
  try {
    await adminApi.delete(`/api/admin/vat-rates/${id}`);
    taxes.value = await getTaxes();
    formerTaxes.value = cloneObject(taxes.value);
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
};

const cancelEdits = (tax: Tax, originalIdx: number) => {
  tax.rate = formerTaxes.value[originalIdx].rate;
  tax.name = formerTaxes.value[originalIdx].name;
  editingId.value = undefined;
};

onMounted(async() => {
  taxes.value = await getTaxes();
  formerTaxes.value = cloneObject(taxes.value);
});

getTaxes();
</script>

<template>
  <div v-if="loading" class="w-full md:min-w-[500px]">
    <PageLoader absolute-center />
  </div>
  <div v-else class="p-4 relative w-full md:min-w-[500px]">
    <Table class="users-table">
      <TableHeader>
        <TableRow>
          <TableHead>
            ID
          </TableHead>
          <TableHead>
            {{ $t('misc.title') }}
          </TableHead>
          <TableHead>
            {{ $t('products.rate') }}
          </TableHead>
          <TableHead class="text-right">
            {{ $t('user-management.actions') }}
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow v-if="creatingNew" class="bg-yellow-100 hover:bg-yellow-100">
          <TableCell />
          <TableCell>
            <Input
              v-model="newItem.name"
              type="text"
              required
              class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500 max-w-[240px]"
            />
          </TableCell>
          <TableCell>
            <Input
              v-model="newItem.rate"
              type="number"
              required
              class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500 max-w-[240px]"
            />
          </TableCell>
          <TableCell class="text-right">
            <div class="flex justify-end items-center flex-nowrap gap-1.5">
              <Button class="size-8 p-1.5 rounded-full bg-green-500 hover:bg-green-600" variant="default" @click="createNewRole">
                <Check class="w-full h-full" />
              </Button>
              <Button class="size-8 p-1.5 rounded-full " variant="destructive" @click="resetNewItem">
                <X class="w-full h-full" />
              </Button>
            </div>
          </TableCell>
        </TableRow>
        <TableRow v-for="(tax, idx) in taxes" :key="tax.id" :class="[editingId === tax.id ? 'bg-yellow-100 hover:bg-yellow-100' : (idx % 2 === 0 ? 'bg-accent' : '')]">
          <TableCell>
            {{ tax.id }}
          </TableCell>
          <TableCell>
            <Input
              v-if="editingId === tax.id"
              v-model="tax.name"
              type="text"
              required
              class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500 min-w-[7rem] max-w-[240px]"
            />
            <div v-else class="w-full min-w-[7rem] max-w-[240px]">{{ tax.name }}</div>
          </TableCell>
          <TableCell>
            <Input
              v-if="editingId === tax.id"
              v-model="tax.rate"
              type="number"
              required
              class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500 min-w-[7rem] max-w-[240px]"
            />
            <div v-else>{{ tax.rate?.toFixed(2) }}%</div>
          </TableCell>
          <TableCell class="text-right w-36">
            <div v-if="editingId === tax.id" class="inline-flex items-center gap-1">
              <Button class="size-8 p-1.5 rounded-full bg-green-500 hover:bg-green-600" variant="default" @click="saveEdits(tax)">
                <Check class="w-full h-full" />
              </Button>
              <Button class="size-8 p-1.5 rounded-full " @click="cancelEdits(tax, idx)">
                <X class="w-full h-full" />
              </Button>
            </div>
            <AlertDialog v-if="editingId !== tax.id">
              <Button class="size-8 p-1.5 rounded-full mr-1" @click="editingId = tax.id">
                <Edit class="w-full h-full" />
              </Button>
              <AlertDialogTrigger>
                <Button class="size-8 p-1.5 rounded-full" variant="destructive">
                  <Trash2 class="w-full h-full" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>{{ $t('misc.delete-action', { name: tax.name }) }}</AlertDialogTitle>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>{{ $t('misc.cancel') }}</AlertDialogCancel>
                  <AlertDialogAction class="bg-destructive text-destructive-foreground hover:bg-destructive/90" @click="removeTax(tax.id)">{{ $t('misc.continue') }}</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </TableCell>
        </TableRow>
      </TableBody>
    </Table>

    <Button class="w-full mt-4" @click="creatingNew = true">
      {{ $t('misc.add') }}
    </Button>
  </div>
</template>
