<script setup lang="ts">
import { useEcho } from '@laravel/echo-vue';
import { onMounted, onUnmounted, ref } from 'vue';
import AntikLogo from '@/assets/svg/antik_logo.svg';
import { useCashRegisterStore } from '@/stores/cash-register';
import { getFromMultiLangObject } from '@/util/multilang';
import type { CROrder, Tax, WSEvent } from '@/util/types/api-responses';

const crStore = useCashRegisterStore();

const items = ref<CROrder['items']>([]);
const total = ref(0);
const vat = ref(0);

const { listen, stopListening } = useEcho(`cash_register.${crStore.crID}.frontend.${crStore.feID}`, ['CashRegisterFrontendEvent'], (e: WSEvent) => {
// const { listen, stopListening } = useEcho(`cash_register.01k092keg7bw7800mvtf5w0sew.frontend.01k092kegpmwzb1adjf7q7xq18`, ['CashRegisterFrontendEvent'], (e: WSEvent) => {
  console.log('Received event:', e);

  const { action, data } = e;
  if (action === 'order_updated') {
    items.value = data.items;
    vat.value = data.items.reduce((sum, item) => sum + item.product.price * item.quantity * ((item.product.tax_id as unknown as Tax).rate / 100), 0);
    total.value = data.items.reduce((sum, item) => sum + item.product.price * item.quantity, 0);
  }
});

onMounted(listen);
onUnmounted(stopListening);
</script>

<template>
  <main class="bg-black text-white">
    <div v-if="items.length" class="grid h-screen grid-cols-[45%_1fr]">
      <section class="p-6 space-y-3">
        <div v-for="{ quantity, product } in items" :key="product.id" class="flex items-center justify-between border-b border-neutral-600 pb-3">
          <div>
            <div class="fig text-2xl">
              <div class="text-neutral-200">{{ quantity }}x</div>
              <div class="font-medium line-clamp-2">{{ getFromMultiLangObject(product.name) }}</div>
            </div>
            <div v-if="quantity > 1" class="text-neutral-400 text-lg">{{ product.price }} €</div>
          </div>

          <div class="text-2xl font-bold">{{ (product.price * quantity).toFixed(2) }} €</div>
        </div>
      </section>

      <section class="flex flex-col">
        <div class="flex-1 grid place-content-center">
          <AntikLogo class="w-full" />
        </div>

        <div class="p-6 space-y-4 w-1/2 ml-auto">
          <h2 class="text-2xl font-bold">{{ $t('cash-register.summary') }}</h2>
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <div class="text-neutral-400">{{ $t('cash-register.subtotal') }}</div>
              <div class="font-medium">{{ (total - vat).toFixed(2) }} €</div>
            </div>
            <div class="flex items-center justify-between">
              <div class="text-neutral-400">{{ $t('cash-register.vat') }}</div>
              <div class="font-medium">{{ vat.toFixed(2) }} €</div>
            </div>
          </div>
          <div class="flex items-center justify-between border-t pt-4 border-neutral-600 pb-3s">
            <div class="text-2xl font-bold">Total</div>
            <div class="text-2xl font-bold text-green-400">{{ total.toFixed(2) }} €</div>
          </div>
        </div>
      </section>
    </div>

    <div v-else class="h-screen w-full grid place-content-center">
      <AntikLogo class="w-full" />
    </div>
  </main>
</template>
