import type { UserData } from '@/stores/auth-store';
import type { UserData as CashUserData } from '@/stores/cash-auth-store';
import type { PermissionData, RoleData } from '@/util/types/roles-and-permissions';

export type OAuthTokenResponse = {
  token_type: string,
  expires_in: number,
  access_token: string,
  refresh_token: string,
}

export type OAuthUserResponse = {
  data: UserData
}

export type OAuthCashUserResponse = {
  data: CashUserData
}

export type Link = {
  first: string;
  last: string;
  prev: string | null;
  next: string | null;
}

export type ApiUsersResponse = {
  data: UserData[];
  links: Link;
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    links: Link[];
    path: string;
    per_page: number;
    to: number;
    total: number;
  };
}

export type ApiRolesResponse = {
  data: RoleData[];
}

export type ApiPermissionsResponse = {
  data: PermissionData[];
}

export type Response<T> = {
  data: T;
  links: Link;
  meta: Meta
}

export interface Meta {
  current_page: number;
  from: number;
  last_page: number;
  links: Link[];
  path: string;
  per_page: number;
  to: number;
  total: number;
}

export interface MultiLang {
  en: string;
  sk: string;
}

export interface Attachment {
  id?: number;
  type_id?: string | null;
  type?: {
    id: string;
    name: MultiLang;
  }
  note?: string;
  created_at?: string;
  updated_at?: string;
  items: Array<{
    id?: number;
    filename: string;
    url?: string;
    url_thumb: string;
    file_original_name?: string;
    size?: string;
    mime_type?: string;
    created_at?: string;
    updated_at?: string;
  }>;
}

export interface Product {
  id: string;
  type: 'product' | 'service' | 'deposit_packaging' | 'ticket';
  image?: string;
  cover_image_thumb?: string;
  cover_image_url?: string;
  supplier_name: string | undefined;
  name: MultiLang;
  description: MultiLang;
  active: boolean;
  code: string;
  price: number;
  plu: number;
  ean: string;
  tax_id: number;
  tax?: Tax;
  unit_id: number;
  unit?: Unit;
  created?: string;
  updated?: string;
  categories: string[] | Category[];
}

export interface Unit {
  id: string
  name: MultiLang;
  shortcut: MultiLang;
}

export interface AttachmentType {
  id: string
  name: MultiLang;
}

export interface Tax {
  id: string
  name: string
  rate: number
}

export interface Category {
  id: string
  name: MultiLang
  color: string
  parent_id: string | null
}

export type Tree = {
  id: string;
  label: MultiLang;
  color?: string;
  children: Tree[];
}[]

export interface ProductFamily {
  id: string;
  name: MultiLang;
  attachments: Attachment[];
}

export interface ProductEmanStock {
  cn?: string;
  warehouses?: ProductEmanStockWarehouse[];
  employees?: ProductEmanStockEmployee[];
}

export interface ProductEmanStockWarehouse {
  name: string;
  stocked: number;
  count: number;
}

export interface ProductEmanStockEmployee {
  name: string;
  count: number;
  receipt_date?: string;
  return_data?: string;
}

export enum CRType {
  CASHIER = 'CASHIER',
  CUSTOMER = 'CUSTOMER',
}

export interface CRFrontend {
  id: string,
  type: CRType,
  display: string,
  resolution: {
    width: number,
    height: number
  }
  created_at: string
}

export interface CR {
  active: boolean
  created_at: string
  device_id: string
  frontends: CRFrontend[]
  id: string
  ip_address: string
  layout: CRLayout
  name: string
  updated_at: string
}

export interface CRComponent {
  type: string
  x: number
  y: number
  w: number
  h: number
}

export interface CRLayout {
  type?: CRType;
  components?: CRComponent[];
}

export interface CRCartItem {
  id: string
  cartItemID: string
  name: MultiLang
  type: Product['type']
  price: number
  quantity: number
}

export interface CROrder {
  id: string;
  items: Array<{
    id: string;
    product: Product;
    model: {
      id: string;
      type: string;
    }
    quantity: number;
  }>;
}

export interface WSEvent {
  action: string;
  data: CROrder;
}

export interface CRGroup {
  id: string
  name: string;
  description: string;
  cash_registers: CR[]
  categories: Category[]
}
