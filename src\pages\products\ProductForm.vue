<script setup lang="ts">
import { Save } from 'lucide-vue-next';
import { computed, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import { routeMap } from '@/router/routes';
import Button from '@/shadcn-components/ui/button/Button.vue';
import Input from '@/shadcn-components/ui/inputs/Input.vue';
import Select from '@/shadcn-components/ui/inputs/Select.vue';
import Textarea from '@/shadcn-components/ui/inputs/Textarea.vue';
import TreeSelect from '@/shadcn-components/ui/inputs/TreeSelect.vue';
import { Label } from '@/shadcn-components/ui/label';
import { ImageUpload } from '@/shadcn-components/ui/uploaders';
import { useAuthStore } from '@/stores/auth-store';
import adminApi from '@/util/adminAxios';
import { formatDate } from '@/util/datetime';
import { deployToast, ToastType } from '@/util/toast';
import type { Category, MultiLang, Tree, Unit, Tax } from '@/util/types/api-responses';

const authStore = useAuthStore();

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

const isEdit = (route.name as string).includes('edit');
const lastEdited = ref<string | null>(null);
const coverImageUrl = ref<string | null>(null);
const units = ref<Unit[]>([]);
const taxes = ref<Tax[]>([]);
const categoryTree = ref<Tree>([]);

const productTypes = [{ value: 'product', label: t('products.product') }, { value: 'service', label: t('products.service') }, { value: 'deposit_packaging', label: t('products.deposit-packaging') }, { value: 'ticket', label: t('products.ticket') }] as const;
const product = reactive({
  type: '' as typeof productTypes[number]['value'],
  code: '',
  plu: null as unknown as number,
  ean: '',
  active: false,
  supplier_name: '',
  price: null as unknown as number,
  tax_id: null,
  unit_id: null,
  image: '',
  categories: [],
  name: {
    sk: '',
    en: '',
  } as MultiLang,
  description: {
    sk: '',
    en: '',
  } as MultiLang,
});
const errors = ref<Record<string, string[]>>({});

const submit = async() => {
  try {
    if (isEdit) {
      await adminApi.put(`/api/admin/products/${route.params.id}`, product);
    } else {
      await adminApi.post('/api/admin/products', product);
    }

    deployToast(ToastType.SUCCESS, {
      text: t('misc.form-sent-successfully'),
      timeout: 6000,
    });
    await router.replace({ name: routeMap.products.children.list.name });
  } catch (e) {
    // @ts-ignore
    errors.value = e?.response?.data?.errors;
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
};

if (isEdit) {
  const { data } = await adminApi.get(`/api/admin/products/${route.params.id}`);
  product.type = data.data.type;
  product.categories = data.data.categories?.map((category: Category) => category.id) ?? [];
  product.name = data.data.name;
  product.description = data.data.description;
  product.unit_id = data.data.unit_id?.id;
  product.tax_id = data.data.tax_id?.id;
  product.code = data.data.code;
  product.plu = data.data.plu;
  product.ean = data.data.ean;
  product.active = data.data.active;
  product.price = data.data.price;
  product.supplier_name = data.data.supplier_name;

  coverImageUrl.value = data.data.cover_image_url;
  lastEdited.value = data.data.updated;
}

try {
  const [
    { data: unts },
    { data: txs },
    { data: tree },
  ] = await Promise.all([
    adminApi.get('/api/admin/units'),
    adminApi.get('/api/admin/vat-rates'),
    adminApi.get('/api/admin/products/categories/tree'),
  ]);

  units.value = unts.data;
  taxes.value = txs.data;
  categoryTree.value = tree.data;
} catch (e) {
  deployToast(ToastType.ERROR, {
    text: t('misc.error'),
    timeout: 6000,
  });
}

const disableByPermission = computed(() => !authStore.hasPermission('product manage'));
</script>

<template>
  <form disabled @submit.prevent>
    <div class="flex flex-col md:grid md:grid-cols-[2fr_1fr] gap-4">
      <div class="flex flex-col gap-4 mb-4">
        <h2 class="font-bold text-2xl text-paynes-gray-400">{{ $t('products.general-information') }}</h2>
        <div v-if="lastEdited" class="flex items-center text-sm font-medium leading-nones gap-2 text-neutral-500 -mt-4">
          <div>{{ $t('products.last-modified') }}:</div>
          <div>{{ formatDate(lastEdited) }}</div>
        </div>

        <div class="flex items-center gap-4">
          <Label for="name" class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('products.name') }}
          </Label>
          <Input
            id="name"
            v-model="product.name"
            :errors="errors?.name"
            type="text"
            multilang
            :disabled="disableByPermission"
          />
        </div>

        <div class="flex items-center gap-4">
          <Label for="description" class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('misc.description') }}
          </Label>
          <Textarea
            id="description"
            v-model="product.description"
            :errors="errors?.description"
            multilang
            :disabled="disableByPermission"
          />
        </div>

        <div class="flex items-center gap-4">
          <Label for="type" class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('misc.type') }}
          </Label>
          <Select
            id="type"
            v-model="product.type"
            :options="productTypes"
            :errors="errors?.type"
            item-title="label"
            item-value="value"
            :disabled="disableByPermission"
          />
        </div>

        <div class="flex gap-4">
          <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32 mt-3">
            {{ $t('products.categories') }}
          </Label>
          <TreeSelect
            v-model="product.categories"
            :options="categoryTree"
            :errors="errors?.name"
            multiple
            :disabled="disableByPermission"
          />
        </div>
      </div>

      <ImageUpload
        :disabled="disableByPermission"
        :cover-image-url="coverImageUrl"
        @get-img="(img: string) => product.image = img"
      />
    </div>

    <div class="flex flex-col gap-6 md:gap-4">
      <h2 class="font-bold text-2xl text-paynes-gray-400">{{ $t('products.about-product') }}</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
        <div class="flex items-center gap-4">
          <Label for="active" class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('products.active') }}
          </Label>
          <el-switch
            id="active"
            v-model="product.active"
            :errors="errors?.active"
            :disabled="disableByPermission"
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
          />
        </div>

        <div class="flex gap-4">
          <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32 mt-3">
            {{ $t('misc.code') }}
          </Label>
          <Input
            v-model="product.code"
            :errors="errors?.code"
            multiple
            :disabled="disableByPermission"
          />
        </div>

        <div class="flex items-center gap-4">
          <Label for="plu" class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            PLU
          </Label>
          <Input
            id="plu"
            v-model="product.plu"
            :errors="errors?.plu"
            :disabled="disableByPermission"
          />
        </div>

        <div class="flex items-center gap-4">
          <Label for="hc_code" class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            EAN
          </Label>
          <Input
            id="ean"
            v-model="product.ean"
            :errors="errors?.ean"
            :disabled="disableByPermission"
          />
        </div>

        <div class="flex items-center gap-4">
          <Label for="supplier_name" class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('products.supplier') }}
          </Label>
          <Input
            id="supplier_name"
            v-model="product.supplier_name"
            :errors="errors?.supplier_name"
            :disabled="disableByPermission"
          />
        </div>

        <div class="flex gap-4">
          <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32 mt-3">
            {{ $t('products.unit') }}
          </Label>
          <Select
            v-model="product.unit_id"
            :errors="errors?.unit_id"
            :options="units"
            :disabled="disableByPermission"
            item-title="name"
            item-value="id"
            multilang
          />
        </div>

        <div class="flex gap-4">
          <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32 mt-3">
            {{ $t('settings.taxesTitle') }}
          </Label>
          <Select
            v-model="product.tax_id"
            :errors="errors?.tax_id"
            :options="taxes"
            :disabled="disableByPermission"
            item-title="name"
            item-value="id"
          />
        </div>

        <div class="flex items-center gap-4">
          <Label for="price" class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('products.price') }}
          </Label>
          <Input
            id="price"
            v-model="product.price"
            :errors="errors?.price"
            :disabled="disableByPermission"
          />
        </div>
      </div>
    </div>

    <div class="flex justify-end mt-8">
      <Button :disabled="disableByPermission" class="w-fit space-x-2 bg-green-600 hover:bg-green-700 ml-auto" @click.prevent="submit">
        <Save v-if="isEdit" stroke-width="1.5" />
        <span>{{ isEdit ? $t('misc.save') : $t('misc.create') }}</span>
      </Button>
    </div>
  </form>
</template>
