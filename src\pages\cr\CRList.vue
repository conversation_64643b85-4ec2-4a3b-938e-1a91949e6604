<script setup lang="ts">
import { IconLayoutFilled, IconEdit, IconCircleXFilled, IconCircleCheckFilled } from '@tabler/icons-vue';
import { useRouteQuery } from '@vueuse/router';
import debounce from 'lodash-es/debounce';
import { Trash2, Search, Check, X } from 'lucide-vue-next';
import { ref } from 'vue';
import PageLoader from '@/components/global/PageLoader.vue';
import Pagination from '@/components/global/Pagination.vue';
import { routeMap } from '@/router/routes';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/shadcn-components/ui/alert-dialog';
import { Button as ShadCnButton } from '@/shadcn-components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import adminApi from '@/util/adminAxios';
import { formatDate } from '@/util/datetime';
import { getFromMultiLangObject } from '@/util/multilang';
import type { CR, Meta, Response } from '@/util/types/api-responses';
import { ComponentStateType } from '@/util/types/components';

const componentState = ref(ComponentStateType.LOADING);

const crs = ref<CR[]>([]);
const editingIdx = ref();
const editRowRef = ref();
const editName = ref('');
const activeState = ref(false);
const paginationMeta = ref<Meta>();
const currentPage = useRouteQuery('page');
const currentLimit = useRouteQuery('limit');

const startEdit = (idx: number, name: string, active: boolean) => {
  editName.value = name;
  editingIdx.value = idx;
  activeState.value = active;
  setTimeout(() => editRowRef.value[idx]?.focus());
};

const saveEdit = async(row: CR) => {
  await adminApi.put(`/api/admin/cash-registers/${row.id}`, {
    ...row,
    name: editName.value,
    active: activeState.value,
  });

  editingIdx.value = undefined;
  fetchData();
};

const fetchData = async(args?: { page?: number, limit?: number, search?: string }) => {
  const to = setTimeout(() => {
    componentState.value = ComponentStateType.LOADING;
  }, 220);
  try {
    const params = {
      page: (args?.page ?? currentPage.value) ?? 1,
      limit: (args?.limit ?? currentLimit.value) ?? 15,
    } as Record<string, string | number>;
    if (args?.search && args.search.length > 2) {
      params.search = args.search;
      delete params.page;
    }

    const { data } = await adminApi.get<Response<CR[]>>('/api/admin/cash-registers', { params });

    crs.value = data.data;
    paginationMeta.value = data.meta;
    clearTimeout(to);
    componentState.value = ComponentStateType.OK;
  } catch {
    clearTimeout(to);
    componentState.value = ComponentStateType.ERROR;
  }
};

await fetchData();

const removeProduct = async(id: string) => {
  try {
    await adminApi.delete(`/api/admin/cash-registers/${id}`);
    await fetchData();
  } catch {
    componentState.value = ComponentStateType.ERROR;
  }
};

const onSearchInput = debounce((ev: Record<any, any>) => {
  fetchData({ search: ev.target?.value });
}, 500);
</script>

<template>
  <div class="flex flex-col">
    <div v-if="componentState === ComponentStateType.OK" class="flex flex-col">
      <div class="flex items-center gap-4 mb-2 flex-wrap">
        <div class="relative p-2 bg-white border-gray-300 border rounded-lg flex items-center w-64 overflow-hidden flex-1 sm:flex-none">
          <div class="pl-0.5 pr-1.5">
            <Search class="min-h-5 h-5 min-w-5 w-5 text-muted-foreground bg-mute" />
          </div>
          <input
            type="text"
            :placeholder="$t('misc.search')"
            class="w-full focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-gray-500 outline-none placeholder:text-sm"
            @input="onSearchInput"
          >
        </div>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{{ $t('products.name') }}</TableHead>
            <TableHead>{{ $t('cash-register.device-id') }}</TableHead>
            <TableHead>{{ $t('misc.created') }}</TableHead>
            <TableHead>{{ $t('cash-register.active') }}</TableHead>
            <TableHead class="text-right">
              {{ $t('user-management.actions') }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(cr, idx) in crs" :key="idx" :class="[editingIdx === idx && 'bg-yellow-100 !bg-yellow-100']">
            <TableCell v-if="editingIdx === idx">
              <input
                ref="editRowRef"
                v-model="editName"
                type="text"
                class="flex h-8 w-full rounded-md border border-input bg-background px-3 shadow py-2 text-sm disabled:cursor-not-allowed disabled:opacity-50 focus:outline-paynes-gray-900 max-w-[340px]"
                @keyup.enter="saveEdit(cr)"
              >
            </TableCell>
            <TableCell v-else-if="cr.name">
              {{ getFromMultiLangObject(cr.name).value }}
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell v-if="cr.device_id">
              <div>... {{ cr.device_id.slice(-6) }}</div>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell v-if="cr.created_at">
              <div>{{ formatDate(cr.created_at) }}</div>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell v-if="editingIdx === idx" class="w-6">
              <el-switch v-model="activeState" />
            </TableCell>
            <TableCell v-else class="w-6">
              <IconCircleCheckFilled v-if="cr.active" class="text-green-500 mx-auto" />
              <IconCircleXFilled v-else class="text-red-500 mx-auto" />
            </TableCell>

            <TableCell v-if="editingIdx === idx" class="text-rights w-[110px] ml-auto flex gap-2 justify-end">
              <div class="inline-flex items-center">
                <button class="size-8 p-1.5 rounded-full bg-green-500 text-primary-foreground hover:bg-green-500/90" @click="saveEdit(cr)">
                  <Check class="w-full h-full" />
                </button>
              </div>
              <div class="inline-flex items-center">
                <button class="size-8 p-1.5 rounded-full bg-rose-600 text-primary-foreground hover:bg-rose-600/90" @click="editingIdx = undefined">
                  <X class="w-full h-full" />
                </button>
              </div>
            </TableCell>

            <TableCell v-else class="text-rights w-[110px] ml-auto flex gap-2 justify-end">
              <div class="inline-flex items-center">
                <button class="size-8 p-1.5 rounded-full bg-paynes-gray-600 text-primary-foreground hover:bg-paynes-gray-600/90" @click="startEdit(idx, cr.name, cr.active)">
                  <IconEdit class="w-full h-full" />
                </button>
              </div>
              <div class="inline-flex items-center">
                <router-link :to="{ name: routeMap.cr.children.editor.name, params: { id: cr.id } }" class="size-8 p-1.5 rounded-full bg-paynes-gray text-primary-foreground hover:bg-paynes-gray/90">
                  <IconLayoutFilled class="w-full h-full" />
                </router-link>
              </div>

              <AlertDialog>
                <AlertDialogTrigger @click.prevent.stop>
                  <ShadCnButton class="w-8 h-8 p-1.5 rounded-full" variant="destructive">
                    <Trash2 class="w-full h-full" />
                  </ShadCnButton>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>{{ $t('form.delete-cr', { name: cr.name }) }}</AlertDialogTitle>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>{{ $t('misc.cancel') }}</AlertDialogCancel>
                    <AlertDialogAction class="bg-destructive text-destructive-foreground hover:bg-destructive/90" @click="removeProduct(cr.id)">{{ $t('misc.continue') }}</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <Pagination v-if="crs?.length" :meta="paginationMeta!" @new-page="fetchData" />

      <div v-if="crs?.length === 0" class="text-sm text-gray-400 w-full text-center mt-4">
        {{ $t('misc.list-is-empty') }}
      </div>
    </div>
    <div v-if="componentState === ComponentStateType.LOADING" class="absolute bg-white/70 top-0 left-0 w-full h-full rounded-3xl">
      <PageLoader :fixed-center="true" />
    </div>
    <div v-else-if="componentState === ComponentStateType.ERROR" class="absolute-center bg-black text-white font-bold p-1">
      <span>{{ $t('misc.failed-to-get-data') }}!</span>
    </div>
  </div>
</template>
