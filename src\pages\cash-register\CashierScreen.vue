<script setup lang="ts">
import { ref } from 'vue';
import Admin from '@/pages/cash-register/components/Admin.vue';
import Categories from '@/pages/cash-register/components/Categories.vue';
import Keypad from '@/pages/cash-register/components/Keypad.vue';
import List from '@/pages/cash-register/components/List.vue';
import { useCashRegisterStore } from '@/stores/cash-register';

const cashRegisterStore = useCashRegisterStore();

const componentMap = {
  'keypad': Keypad,
  'list': List,
  'categories': Categories,
  'admin': Admin,
};

const components = ref(cashRegisterStore.layout?.components!.map(comp => ({
  component: componentMap[comp.type],
  style: {
    gridColumn: `${comp.x + 1} / span ${comp.w}`,
    gridRow: `${comp.y + 1} / span ${comp.h}`,
  },
})));
</script>

<template>
  <main class="h-screen grid grid-cols-12 grid-rows-12">
    <component :is="component" v-for="({ style, component }, idx) in components" :key="idx" :style />
  </main>
</template>
