<script setup lang="ts">
import { IconBackspace } from '@tabler/icons-vue';

const emits = defineEmits(['addDigit', 'deleteDigit']);

const keys = ['1', '2', '3', '4', '5', '6', '7', '8', '9'];
</script>

<template>
  <div class="grid grid-cols-3 gap-3 place-items-center w-[240px] mx-auto select-none">
    <button v-for="digit in keys" :key="digit" class="bg-white text-3xl font-medium size-[4.5rem] rounded-xl shadow-md active:shadow active:scale-[0.99] border border-black/20" @click="emits('addDigit', digit)">{{ digit }}</button>
    <button class="bg-white font-medium size-[4.5rem] rounded-xl shadow-md active:shadow active:scale-[0.99] border border-black/20 grid place-items-center" @click="emits('deleteDigit')">
      <IconBackspace size="30" class="mr-0.5 text-rose-900" />
    </button>
    <button class="bg-white text-3xl font-medium size-[4.5rem] rounded-xl shadow-md active:shadow active:scale-[0.99] border border-black/20" @click="emits('addDigit', '0')">0</button>
    <slot />
  </div>
</template>
