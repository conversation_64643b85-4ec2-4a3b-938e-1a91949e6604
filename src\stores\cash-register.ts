import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { CRType, CR, CRLayout, CRCartItem } from '@/util/types/api-responses';

export const useCashRegisterStore = defineStore('cash-register', () => {
  const layout = ref<CRLayout>();
  const crID = ref<string>();
  const feID = ref<string>();
  const type = ref<CRType>();
  const cart = ref<CRCartItem[]>([]);
  const currentCartID = ref<string>();

  const init = (actualDisplay: number, data: CR) => {
    const fe = data.frontends.find(f => f.display === actualDisplay.toString(10));
    crID.value = data.id;
    feID.value = fe?.id;
    type.value = fe?.type;
    layout.value = data.layout;
  };

  return {
    layout,
    feID,
    crID,
    type,
    cart,
    currentCartID,
    init,
  };
});
