<script setup lang="ts">
import { useIdle } from '@vueuse/core';
import { onBeforeUnmount, onMounted, ref, watch } from 'vue';
import PageLoader from '@/components/global/PageLoader.vue';
import { AndroidActions, handleAndroidEvent, requestAndroid } from '@/pages/cash-register/androidRequestHandler';
import CashierScreen from '@/pages/cash-register/CashierScreen.vue';
import CustomerScreen from '@/pages/cash-register/CustomerScreen.vue';
import CashRegisterLoginModal from '@/pages/cash-register/dialogs/CashRegisterLoginModal.vue';
import { useCashAuthStore } from '@/stores/cash-auth-store';
import { useCashRegisterStore } from '@/stores/cash-register';
import { CRType } from '@/util/types/api-responses';

const cashAuthStore = useCashAuthStore();
const crStore = useCashRegisterStore();

const loading = ref(true);
const showLogin = ref(false);

const { idle, reset } = useIdle(5 * 60 * 1000); // 5 min
watch(idle, (idleValue) => {
  if (import.meta.env.DEV) {
    return;
  }

  if (idleValue && crStore.type === CRType.CASHIER) {
    showLogin.value = true;
  }
});

watch(() => crStore.crID, async() => {
  if (crStore.type === CRType.CASHIER) {
    await cashAuthStore.fetchUser();
    showLogin.value = Boolean(!cashAuthStore.user);
  }
  loading.value = false;
});

onMounted(async() => {
  requestAndroid({ action: AndroidActions.INIT });
  window.addEventListener('android-data', handleAndroidEvent);

  if (import.meta.env.DEV) {
    setTimeout(() => {
      window.receiveResponse('{"action":"deviceSettings","actualDisplay":0,"displays":[{"id":0,"resolution":{"height":1024,"width":1920}},{"id":1,"resolution":{"height":1080,"width":1920}}],"uuid":"e92547aa-c7bd-4ec0-974a-5effd725134a","versionApp":"1.0.0"}');
    }, 1000);
  }
});
onBeforeUnmount(() => window.removeEventListener('android-data', handleAndroidEvent));
</script>

<template>
  <PageLoader v-if="loading" absolute-center />
  <CashierScreen v-else-if="crStore.type === CRType.CASHIER" :key="cashAuthStore.user?.id ?? 1" />
  <CustomerScreen v-else-if="crStore.type === CRType.CUSTOMER" />
  <CashRegisterLoginModal v-model="showLogin" @on-success="reset" />
</template>
